# AveImg Cloud - Image Hosting Platform

A modern image hosting platform built with Next.js 15, TypeScript, Tailwind CSS v4, and Appwrite backend.

## Features

- 🔐 **Discord OAuth Authentication** - Seamless single sign-on with Discord integration
- 🎨 **Modern UI** - Dark theme with cyan-blue color palette
- 📱 **Responsive Design** - Works perfectly on all devices
- 🚀 **Fast Performance** - Built with Next.js 15 and optimized for speed
- 🔒 **Secure** - Protected routes and OAuth-based authentication
- 📊 **Dashboard** - User-friendly dashboard with statistics and quick actions

## Tech Stack

- **Frontend**: Next.js 15, TypeScript, Tailwind CSS v4
- **Backend**: Appwrite (Custom domain: api.avehubs.com/v1)
- **Authentication**: Discord OAuth via Appwrite Auth
- **Database**: Appwrite Database
- **Storage**: Appwrite Storage

## Project Structure

```
├── app/                    # Next.js 15 App Router
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Dashboard pages
│   └── api/               # API routes
├── components/            # React components
│   ├── ui/                # Reusable UI components
│   ├── auth/              # Authentication components
│   └── dashboard/         # Dashboard components
├── hooks/                 # Custom React hooks
├── lib/                   # Utilities and configurations
│   ├── appwrite/          # Appwrite configuration and services
│   └── utils/             # Utility functions
├── types/                 # TypeScript type definitions
├── public/                # Static assets
└── styles/                # Additional CSS files
```

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Appwrite account and project

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd aveimgcloud1
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Fill in your Appwrite configuration:
   ```env
   NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id_here
   NEXT_PUBLIC_APPWRITE_DATABASE_ID=your_database_id_here
   NEXT_PUBLIC_APPWRITE_USER_COLLECTION_ID=your_user_collection_id_here
   NEXT_PUBLIC_APPWRITE_IMAGE_COLLECTION_ID=your_image_collection_id_here
   NEXT_PUBLIC_APPWRITE_STORAGE_ID=your_storage_id_here
   ```

4. **Configure Discord OAuth**

   **Step 1: Create Discord Application**
   - Go to [Discord Developer Portal](https://discord.com/developers/applications)
   - Click "New Application" and give it a name
   - Go to the "OAuth2" tab and copy your Client ID and Client Secret

   **Step 2: Configure Appwrite**
   - In your [Appwrite Console](https://cloud.appwrite.io), go to Auth → Settings
   - Find "Discord" under OAuth2 Providers and click to configure
   - Enter your Discord Client ID and Client Secret
   - Copy the redirect URI provided by Appwrite

   **Step 3: Configure Discord Redirects**
   - Back in Discord Developer Portal, go to OAuth2 → Redirects
   - Add the redirect URI from Appwrite (should look like: `https://[REGION].cloud.appwrite.io/v1/account/sessions/oauth2/callback/discord/[PROJECT_ID]`)
   - For local development, also add: `http://localhost:3000/auth/callback`

5. **Configure Appwrite Project**
   - Create a new project in [Appwrite Console](https://cloud.appwrite.io)
   - Create necessary databases and collections
   - Configure storage bucket
   - Add your domain to the platform settings (localhost:3000 for development)

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Authentication Flow

1. **Discord OAuth**: Users authenticate using their Discord account
2. **Automatic Account Creation**: First-time users get accounts created automatically from Discord profile
3. **Protected Routes**: Dashboard and user-specific pages require authentication
4. **Session Management**: Automatic session handling with Appwrite OAuth
5. **Callback Handling**: Seamless redirect flow after Discord authentication

## Dashboard Features

- **Welcome Message**: Personalized greeting with user's name
- **Statistics Cards**: Display user metrics (images, views, likes, storage)
- **Quick Actions**: Easy access to upload, view images, and settings
- **Responsive Layout**: Header with user menu and sidebar navigation

## Deployment

The application is ready for deployment on platforms like Vercel, Netlify, or any Node.js hosting service.

### Deploy on Vercel

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy!

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support, please open an issue in the GitHub repository or contact the development team.
