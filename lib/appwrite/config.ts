import { Client, Account, Databases, Storage } from 'appwrite';

// Appwrite configuration
export const appwriteConfig = {
  endpoint: 'https://api.avehubs.com/v1',
  projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '',
  databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || '',
  userCollectionId: process.env.NEXT_PUBLIC_APPWRITE_USER_COLLECTION_ID || '',
  userProfilesCollectionId: process.env.NEXT_PUBLIC_APPWRITE_USER_PROFILES_COLLECTION_ID || '',
  imageCollectionId: process.env.NEXT_PUBLIC_APPWRITE_IMAGE_COLLECTION_ID || '',
  uploadedImagesCollectionId: process.env.NEXT_PUBLIC_APPWRITE_UPLOADED_IMAGES_COLLECTION_ID || '',
  storageId: process.env.NEXT_PUBLIC_APPWRITE_STORAGE_ID || '',
};

// Initialize Appwrite client
export const client = new Client();

client
  .setEndpoint(appwriteConfig.endpoint)
  .setProject(appwriteConfig.projectId);

// Initialize Appwrite services
export const account = new Account(client);
export const databases = new Databases(client);
export const storage = new Storage(client);

export { ID } from 'appwrite';
