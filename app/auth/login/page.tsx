import React from 'react';
import DiscordLoginButton from '@/components/auth/DiscordLoginButton';

export default function LoginPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md mx-auto">
        <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Welcome to AveImg Cloud
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Sign in with your Discord account to get started
            </p>
          </div>

          <div className="space-y-6">
            <DiscordLoginButton
              variant="primary"
              size="lg"
              className="w-full"
            />
          </div>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              By signing in, you agree to our Terms of Service and Privacy Policy
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
