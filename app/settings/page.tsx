'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { userService } from '@/lib/services/userService';
import { UserConfiguration, EmbedSettings } from '@/types';
import DashboardLayout from '@/components/dashboard/DashboardLayout';

export default function SettingsPage() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Form state
  const [embedSettings, setEmbedSettings] = useState<EmbedSettings>({
    embedTitle: 'Image Hosted on AveImgCloud',
    embedFooter: 'Powered by AveImgCloud',
    embedColor: '#06b6d4'
  });

  // Load user configuration
  useEffect(() => {
    const loadUserConfig = async () => {
      if (!user) return;
      
      try {
        setIsLoading(true);
        const config = await userService.getUserConfiguration(user.$id);
        
        setEmbedSettings({
          embedTitle: config.embedTitle || 'Image Hosted on AveImgCloud',
          embedFooter: config.embedFooter || 'Powered by AveImgCloud',
          embedColor: config.embedColor || '#06b6d4'
        });
      } catch (error) {
        console.error('Error loading user configuration:', error);
        setError('Failed to load settings');
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      loadUserConfig();
    }
  }, [user]);

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    try {
      setIsSaving(true);
      setError(null);
      setSuccess(false);

      // Validate inputs
      if (!embedSettings.embedTitle.trim()) {
        setError('Embed title is required');
        return;
      }
      if (!embedSettings.embedFooter.trim()) {
        setError('Embed footer is required');
        return;
      }
      if (!embedSettings.embedColor.match(/^#[0-9A-Fa-f]{6}$/)) {
        setError('Please enter a valid hex color code');
        return;
      }

      await userService.updateUserConfiguration(user.$id, {
        embedTitle: embedSettings.embedTitle.trim(),
        embedFooter: embedSettings.embedFooter.trim(),
        embedColor: embedSettings.embedColor
      });

      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (error) {
      console.error('Error saving settings:', error);
      setError('Failed to save settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setEmbedSettings({
      embedTitle: 'Image Hosted on AveImgCloud',
      embedFooter: 'Powered by AveImgCloud',
      embedColor: '#06b6d4'
    });
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Settings</h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Customize your embed settings and account preferences
          </p>
        </div>

        {/* Settings Form */}
        <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Embed Customization
            </h2>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Customize how your images appear when shared on Discord, Twitter, and other platforms
            </p>
          </div>

          <form onSubmit={handleSave} className="p-6 space-y-6">
            {/* Embed Title */}
            <div>
              <label htmlFor="embedTitle" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Embed Title
              </label>
              <input
                type="text"
                id="embedTitle"
                value={embedSettings.embedTitle}
                onChange={(e) => setEmbedSettings(prev => ({ ...prev, embedTitle: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 dark:bg-gray-700 dark:text-white"
                placeholder="Image Hosted on AveImgCloud"
                maxLength={100}
                required
              />
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                This will appear as the title in rich embeds (max 100 characters)
              </p>
            </div>

            {/* Embed Footer */}
            <div>
              <label htmlFor="embedFooter" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Embed Footer
              </label>
              <input
                type="text"
                id="embedFooter"
                value={embedSettings.embedFooter}
                onChange={(e) => setEmbedSettings(prev => ({ ...prev, embedFooter: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 dark:bg-gray-700 dark:text-white"
                placeholder="Powered by AveImgCloud"
                maxLength={50}
                required
              />
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                This will appear as the footer in rich embeds (max 50 characters)
              </p>
            </div>

            {/* Embed Color */}
            <div>
              <label htmlFor="embedColor" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Embed Accent Color
              </label>
              <div className="flex items-center space-x-3">
                <input
                  type="color"
                  id="embedColor"
                  value={embedSettings.embedColor}
                  onChange={(e) => setEmbedSettings(prev => ({ ...prev, embedColor: e.target.value }))}
                  className="w-12 h-10 border border-gray-300 dark:border-gray-600 rounded-md cursor-pointer"
                />
                <input
                  type="text"
                  value={embedSettings.embedColor}
                  onChange={(e) => setEmbedSettings(prev => ({ ...prev, embedColor: e.target.value }))}
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 dark:bg-gray-700 dark:text-white"
                  placeholder="#06b6d4"
                  pattern="^#[0-9A-Fa-f]{6}$"
                  required
                />
              </div>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                This color will appear as the accent color in rich embeds
              </p>
            </div>

            {/* Preview */}
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-900">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Preview</h3>
              <div className="border-l-4 pl-4" style={{ borderColor: embedSettings.embedColor }}>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {embedSettings.embedTitle}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {embedSettings.embedFooter}
                </div>
              </div>
            </div>

            {/* Error/Success Messages */}
            {error && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                <div className="text-sm text-red-600 dark:text-red-400">{error}</div>
              </div>
            )}

            {success && (
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
                <div className="text-sm text-green-600 dark:text-green-400">
                  Settings saved successfully!
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-between pt-4">
              <button
                type="button"
                onClick={handleReset}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500"
              >
                Reset to Defaults
              </button>
              <button
                type="submit"
                disabled={isSaving}
                className="px-6 py-2 text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 disabled:bg-cyan-400 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 transition-colors duration-200"
              >
                {isSaving ? 'Saving...' : 'Save Settings'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </DashboardLayout>
  );
}
