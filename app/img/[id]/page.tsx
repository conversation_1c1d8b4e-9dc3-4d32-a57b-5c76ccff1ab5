import React from 'react';
import { Metadata } from 'next';
import { uploadService } from '@/lib/services/uploadService';
import { userService } from '@/lib/services/userService';
import { notFound } from 'next/navigation';
import ImageDisplayClient from './ImageDisplayClient';

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

// Generate metadata for Open Graph embeds
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  try {
    const { id } = await params;
    const image = await uploadService.getImageByUniqueId(id);
    
    if (!image) {
      return {
        title: 'Image Not Found - AveImgCloud',
        description: 'The requested image could not be found.',
      };
    }

    // Get user's embed settings
    let embedSettings = {
      embedTitle: 'Image Hosted on AveImgCloud',
      embedFooter: 'Powered by AveImgCloud',
      embedColor: '#06b6d4'
    };

    try {
      const userConfig = await userService.getUserConfiguration(image.userId);
      embedSettings = {
        embedTitle: userConfig.embedTitle || embedSettings.embedTitle,
        embedFooter: userConfig.embedFooter || embedSettings.embedFooter,
        embedColor: userConfig.embedColor || embedSettings.embedColor
      };
    } catch (error) {
      console.warn('Could not load user embed settings, using defaults:', error);
    }

    const imageUrl = uploadService.getFileUrl(image.fileId);
    const pageUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/img/${id}`;

    return {
      title: embedSettings.embedTitle,
      description: `${image.filename} - ${embedSettings.embedFooter}`,
      openGraph: {
        title: embedSettings.embedTitle,
        description: `${image.filename} - ${embedSettings.embedFooter}`,
        url: pageUrl,
        siteName: 'AveImgCloud',
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: image.filename,
          },
        ],
        type: 'website',
      },
      twitter: {
        card: 'summary_large_image',
        title: embedSettings.embedTitle,
        description: `${image.filename} - ${embedSettings.embedFooter}`,
        images: [imageUrl],
      },
      other: {
        'theme-color': embedSettings.embedColor,
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'AveImgCloud',
      description: 'Image hosting platform',
    };
  }
}

export default async function ImageDisplayPage({ params }: PageProps) {
  try {
    const image = await uploadService.getImageByUniqueId(params.id);
    
    if (!image) {
      notFound();
    }

    // Get user's embed settings for client-side display
    let embedSettings = {
      embedTitle: 'Image Hosted on AveImgCloud',
      embedFooter: 'Powered by AveImgCloud',
      embedColor: '#06b6d4'
    };

    try {
      const userConfig = await userService.getUserConfiguration(image.userId);
      embedSettings = {
        embedTitle: userConfig.embedTitle || embedSettings.embedTitle,
        embedFooter: userConfig.embedFooter || embedSettings.embedFooter,
        embedColor: userConfig.embedColor || embedSettings.embedColor
      };
    } catch (error) {
      console.warn('Could not load user embed settings, using defaults:', error);
    }

    return <ImageDisplayClient image={image} embedSettings={embedSettings} />;
  } catch (error) {
    console.error('Error loading image:', error);
    notFound();
  }
}
