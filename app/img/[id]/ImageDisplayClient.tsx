'use client';

import React, { useState } from 'react';
import { UploadedImage, EmbedSettings } from '@/types';
import { uploadService } from '@/lib/services/uploadService';
import Link from 'next/link';

interface ImageDisplayClientProps {
  image: UploadedImage;
  embedSettings: EmbedSettings;
}

export default function ImageDisplayClient({ image, embedSettings }: ImageDisplayClientProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const fileUrl = uploadService.getFileUrl(image.fileId);
  const downloadUrl = uploadService.getFileDownloadUrl(image.fileId);
  const isImage = uploadService.isImageFile(image.mimeType);
  const isVideo = uploadService.isVideoFile(image.mimeType);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="text-2xl font-bold text-gray-900 dark:text-white"
              >
                AveImgCloud
              </Link>
              <div className="hidden sm:block w-px h-6 bg-gray-300 dark:bg-gray-600"></div>
              <div className="hidden sm:block">
                <h1 className="text-lg font-medium text-gray-900 dark:text-white truncate">
                  {image.filename}
                </h1>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <a
                href={downloadUrl}
                download={image.filename}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-4-4m4 4l4-4m-4-4V3" />
                </svg>
                Download
              </a>
              <Link
                href="/upload"
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500"
              >
                Upload Your Own
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
              {/* Media Display */}
              <div className="relative bg-gray-100 dark:bg-gray-900 flex items-center justify-center min-h-[400px]">
                {isImage && (
                  <>
                    {!imageLoaded && !imageError && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-600"></div>
                      </div>
                    )}
                    {imageError ? (
                      <div className="text-center p-8">
                        <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        <p className="text-gray-500 dark:text-gray-400">Failed to load image</p>
                      </div>
                    ) : (
                      <img
                        src={fileUrl}
                        alt={image.filename}
                        className={`max-w-full max-h-[80vh] object-contain transition-opacity duration-300 ${
                          imageLoaded ? 'opacity-100' : 'opacity-0'
                        }`}
                        onLoad={() => setImageLoaded(true)}
                        onError={() => setImageError(true)}
                      />
                    )}
                  </>
                )}

                {isVideo && (
                  <video
                    src={fileUrl}
                    controls
                    className="max-w-full max-h-[80vh] object-contain"
                    preload="metadata"
                  >
                    Your browser does not support the video tag.
                  </video>
                )}

                {!isImage && !isVideo && (
                  <div className="text-center p-8">
                    <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p className="text-gray-500 dark:text-gray-400 mb-4">File preview not available</p>
                    <a
                      href={downloadUrl}
                      download={image.filename}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500"
                    >
                      Download File
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 space-y-6">
              {/* File Info */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">File Information</h3>
                <dl className="space-y-3">
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Filename</dt>
                    <dd className="text-sm text-gray-900 dark:text-white break-all">{image.filename}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">File Size</dt>
                    <dd className="text-sm text-gray-900 dark:text-white">{uploadService.formatFileSize(image.fileSize)}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">File Type</dt>
                    <dd className="text-sm text-gray-900 dark:text-white">{image.mimeType}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Uploaded</dt>
                    <dd className="text-sm text-gray-900 dark:text-white">{formatDate(image.uploadDate)}</dd>
                  </div>
                </dl>
              </div>

              {/* Share URL */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Share</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                      Direct Link
                    </label>
                    <div className="flex">
                      <input
                        type="text"
                        readOnly
                        value={`${window.location.origin}/img/${image.uniqueId}`}
                        className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md bg-gray-50 dark:bg-gray-700 text-sm text-gray-600 dark:text-gray-300"
                      />
                      <button
                        onClick={() => copyToClipboard(`${window.location.origin}/img/${image.uniqueId}`)}
                        className="px-3 py-2 border border-l-0 border-gray-300 dark:border-gray-600 rounded-r-md bg-white dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500 text-sm"
                      >
                        Copy
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                      Direct Image URL
                    </label>
                    <div className="flex">
                      <input
                        type="text"
                        readOnly
                        value={fileUrl}
                        className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md bg-gray-50 dark:bg-gray-700 text-sm text-gray-600 dark:text-gray-300"
                      />
                      <button
                        onClick={() => copyToClipboard(fileUrl)}
                        className="px-3 py-2 border border-l-0 border-gray-300 dark:border-gray-600 rounded-r-md bg-white dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500 text-sm"
                      >
                        Copy
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Embed Preview */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Embed Preview</h3>
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-900">
                  <div className="border-l-4 pl-4" style={{ borderColor: embedSettings.embedColor }}>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {embedSettings.embedTitle}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {image.filename} - {embedSettings.embedFooter}
                    </div>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <a
                  href={downloadUrl}
                  download={image.filename}
                  className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-4-4m4 4l4-4m-4-4V3" />
                  </svg>
                  Download File
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
