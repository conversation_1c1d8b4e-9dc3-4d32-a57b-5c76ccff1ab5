'use client';

import React from 'react';
import Header from './Header';
import Sidebar from './Sidebar';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import BackgroundEffects from '@/components/ui/BackgroundEffects';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  return (
    <ProtectedRoute>
      <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-gray-50 via-cyan-50/30 to-blue-50/30 dark:from-gray-900 dark:via-cyan-900/10 dark:to-blue-900/10">
        {/* Background Effects */}
        <BackgroundEffects variant="minimal" intensity="low" />

        <Header />
        <div className="flex relative z-10">
          <Sidebar />
          {/* Main content with proper spacing for fixed sidebar */}
          <main className="flex-1 lg:ml-64 p-4 sm:p-6 lg:p-8 pt-6">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </main>
        </div>
      </div>
    </ProtectedRoute>
  );
};

export default DashboardLayout;
