'use client';

import React, { useEffect, useRef } from 'react';

interface BackgroundEffectsProps {
  variant?: 'particles' | 'waves' | 'geometric' | 'minimal';
  intensity?: 'low' | 'medium' | 'high';
  className?: string;
}

const BackgroundEffects: React.FC<BackgroundEffectsProps> = ({
  variant = 'particles',
  intensity = 'medium',
  className = '',
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Particle system
    const particles: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;
      color: string;
    }> = [];

    const colors = [
      'rgba(6, 182, 212, 0.6)',   // cyan-500
      'rgba(14, 165, 233, 0.6)',  // blue-500
      'rgba(59, 130, 246, 0.6)',  // blue-500
      'rgba(139, 92, 246, 0.6)',  // violet-500
    ];

    const particleCount = intensity === 'low' ? 30 : intensity === 'medium' ? 50 : 80;

    // Initialize particles
    for (let i = 0; i < particleCount; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        size: Math.random() * 3 + 1,
        opacity: Math.random() * 0.5 + 0.2,
        color: colors[Math.floor(Math.random() * colors.length)],
      });
    }

    let animationId: number;

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      if (variant === 'particles') {
        // Update and draw particles
        particles.forEach((particle, index) => {
          // Update position
          particle.x += particle.vx;
          particle.y += particle.vy;

          // Bounce off edges
          if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
          if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

          // Draw particle
          ctx.beginPath();
          ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
          ctx.fillStyle = particle.color;
          ctx.fill();

          // Draw connections
          particles.forEach((otherParticle, otherIndex) => {
            if (index !== otherIndex) {
              const dx = particle.x - otherParticle.x;
              const dy = particle.y - otherParticle.y;
              const distance = Math.sqrt(dx * dx + dy * dy);

              if (distance < 100) {
                ctx.beginPath();
                ctx.moveTo(particle.x, particle.y);
                ctx.lineTo(otherParticle.x, otherParticle.y);
                ctx.strokeStyle = `rgba(6, 182, 212, ${0.1 * (1 - distance / 100)})`;
                ctx.lineWidth = 1;
                ctx.stroke();
              }
            }
          });
        });
      } else if (variant === 'waves') {
        // Draw animated waves
        const time = Date.now() * 0.001;
        const waveCount = 3;
        
        for (let i = 0; i < waveCount; i++) {
          ctx.beginPath();
          ctx.moveTo(0, canvas.height / 2);
          
          for (let x = 0; x <= canvas.width; x += 10) {
            const y = canvas.height / 2 + 
              Math.sin((x * 0.01) + (time * 2) + (i * Math.PI / 3)) * (30 + i * 20) +
              Math.sin((x * 0.005) + (time * 1.5) + (i * Math.PI / 4)) * (20 + i * 10);
            ctx.lineTo(x, y);
          }
          
          ctx.strokeStyle = colors[i % colors.length];
          ctx.lineWidth = 2;
          ctx.stroke();
        }
      } else if (variant === 'geometric') {
        // Draw floating geometric shapes
        const time = Date.now() * 0.001;
        
        for (let i = 0; i < 20; i++) {
          const x = (canvas.width / 20) * i + Math.sin(time + i) * 50;
          const y = canvas.height / 2 + Math.cos(time * 0.7 + i) * 100;
          const size = 20 + Math.sin(time * 2 + i) * 10;
          const rotation = time + i;
          
          ctx.save();
          ctx.translate(x, y);
          ctx.rotate(rotation);
          
          // Draw diamond
          ctx.beginPath();
          ctx.moveTo(0, -size);
          ctx.lineTo(size, 0);
          ctx.lineTo(0, size);
          ctx.lineTo(-size, 0);
          ctx.closePath();
          
          ctx.fillStyle = colors[i % colors.length];
          ctx.fill();
          ctx.restore();
        }
      }

      animationId = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationId);
    };
  }, [variant, intensity]);

  if (variant === 'minimal') {
    return (
      <div className={`fixed inset-0 pointer-events-none z-0 ${className}`}>
        <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/5 via-blue-500/5 to-violet-500/5 animate-gradient" />
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-pulse-slow" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }} />
      </div>
    );
  }

  return (
    <div className={`fixed inset-0 pointer-events-none z-0 ${className}`}>
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full"
        style={{ mixBlendMode: 'screen' }}
      />
      {/* Additional gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-cyan-500/5 to-blue-500/5" />
    </div>
  );
};

export default BackgroundEffects;
