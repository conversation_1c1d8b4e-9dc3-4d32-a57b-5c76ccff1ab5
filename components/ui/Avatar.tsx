'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';

interface AvatarProps {
  src?: string;
  alt?: string;
  fallback?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  onClick?: () => void;
}

const Avatar: React.FC<AvatarProps> = ({
  src,
  alt = 'Avatar',
  fallback,
  size = 'md',
  className = '',
  onClick,
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const sizeClasses = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg',
    xl: 'w-16 h-16 text-xl',
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  const showFallback = !src || imageError;

  return (
    <div
      className={cn(
        'relative inline-flex items-center justify-center rounded-full bg-gradient-to-br from-cyan-500 to-blue-600 text-white font-medium shadow-lg transition-all duration-200',
        sizeClasses[size],
        onClick && 'cursor-pointer hover:shadow-xl hover:scale-105',
        className
      )}
      onClick={onClick}
    >
      {/* Loading state */}
      {isLoading && src && !imageError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-200 dark:bg-gray-700 rounded-full">
          <div className="w-4 h-4 border-2 border-cyan-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}

      {/* Image */}
      {src && !imageError && (
        <img
          src={src}
          alt={alt}
          className={cn(
            'w-full h-full rounded-full object-cover transition-opacity duration-200',
            isLoading ? 'opacity-0' : 'opacity-100'
          )}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      )}

      {/* Fallback */}
      {showFallback && (
        <span className="select-none">
          {fallback || alt?.charAt(0)?.toUpperCase() || '?'}
        </span>
      )}

      {/* Hover overlay */}
      {onClick && (
        <div className="absolute inset-0 rounded-full bg-black bg-opacity-0 hover:bg-opacity-10 transition-all duration-200" />
      )}
    </div>
  );
};

export default Avatar;
